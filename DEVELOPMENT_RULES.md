# GP Stock App - Development Rules & Guidelines

## 🎯 **Theme System Rules**

### **1. Color Usage - MANDATORY**
```dart
// ✅ CORRECT - Always use theme colors
backgroundColor: context.theme.cardColor,
textColor: context.colorTheme.textPrimary,
primaryColor: context.theme.primaryColor,
dividerColor: context.theme.dividerColor,
stockGreen: context.colorTheme.stockGreen,
stockRed: context.colorTheme.stockRed,

// ❌ WRONG - Never hardcode colors
backgroundColor: Color(0xFFFFFFFF),
textColor: Color(0xFF525A79),
primaryColor: Color(0xFF4062D8),
```

### **2. Text Styling - MANDATORY**
```dart
// ✅ CORRECT - Use theme with extensions
Text('title'.tr(), style: context.textTheme.primary.fs16.w600)
Text('content'.tr(), style: context.textTheme.title)
Text('subtitle'.tr(), style: context.textTheme.secondary)
Text('caption'.tr(), style: context.textTheme.tertiary)

// ❌ WRONG - Never hardcode text styles
Text('title', style: TextStyle(
  fontFamily: 'PingFang SC',
  fontSize: 16,
  fontWeight: FontWeight.w600,
  color: Color(0xFF525A79),
))
```

### **3. Available Theme Extensions**
- **Text Themes**: `primary`, `title`, `regular`, `secondary`, `tertiary`
- **Font Sizes**: `.fs12`, `.fs14`, `.fs16`, `.fs18`, `.fs20`, etc.
- **Font Weights**: `.w300`, `.w400`, `.w500`, `.w600`, `.w700`, `.w800`
- **Colors**: `textPrimary`, `textTitle`, `textRegular`, `stockGreen`, `stockRed`

## 📏 **Sizing & Spacing Rules**

### **4. Sizing Extensions - CRITICAL**
```dart
// ✅ CORRECT - Use proper extensions
width: 100.gw,           // Width-based sizing
height: 50.gw,           // Height using .gw (NOT .gh!)
borderRadius: 12.gr,     // Radius values
padding: EdgeInsets.all(16.gw),
margin: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 10.gw),

// ❌ WRONG - Never use .gh or hardcoded values
height: 50.gh,           // FORBIDDEN - use .gw instead
width: 100,              // Missing extension
borderRadius: BorderRadius.circular(12), // Missing .gr
```

### **5. Spacing Helpers**
```dart
// ✅ Use spacing extensions
SizedBox(height: 16.verticalSpace),
SizedBox(width: 12.horizontalSpace),
```

## 🏗️ **Dialog Structure Rules**

### **6. Dialog Container Pattern**
```dart
// ✅ CORRECT Dialog Structure
class MyDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.gr),
      ),
      child: Container(
        padding: EdgeInsets.all(20.gw),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(16.gr),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // IMPORTANT!
          children: [
            // Content
          ],
        ),
      ),
    );
  }
}
```

### **7. Required Imports for Dialogs**
```dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
```

## 🔘 **Button Implementation Rules**

### **8. Use Existing Button Widgets**
```dart
// ✅ CORRECT - Use existing buttons
CommonButton(
  title: 'confirm'.tr(),
  style: CommonButtonStyle.primary,
  onPressed: () {},
  height: 44.gw,
)

// ✅ Alternative
CustomMaterialButton(
  title: 'cancel'.tr(),
  style: CustomMaterialButtonStyle.outlined,
  onPressed: () {},
)

// ❌ WRONG - Don't create custom buttons without checking existing ones
Container(
  decoration: BoxDecoration(color: Colors.blue),
  child: Text('Button'),
)
```

## 🌐 **Translation Rules**

### **9. Internationalization - MANDATORY**
```dart
// ✅ CORRECT - Always use .tr()
Text('interestCoupon'.tr())
title: 'confirm'.tr(),
hintText: 'enterAmount'.tr(),

// ❌ WRONG - Never hardcode text
Text('利息券')
Text('Interest Coupon')
title: 'Confirm',
```

### **10. Translation Key Patterns**
- Use descriptive keys: `interestCouponUnused` not `unused`
- Use camelCase: `interestCouponExpired`
- Group related keys: `interestCoupon`, `interestCouponAmount`, `interestCouponUsed`

## 📱 **Modal & Bottom Sheet Rules**

### **11. Modal Bottom Sheet Pattern**
```dart
// ✅ CORRECT Pattern
showModalBottomSheet(
  context: context,
  backgroundColor: Colors.transparent,
  isScrollControlled: true,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
  ),
  builder: (context) => Container(
    decoration: BoxDecoration(
      color: context.theme.cardColor,
      borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
    ),
    child: // Content
  ),
);
```

## 🎨 **Widget Usage Rules**

### **12. Container & Layout Rules**
```dart
// ✅ CORRECT - Use existing widgets
ShadowBox(
  borderRadius: BorderRadius.circular(8.gr),
  padding: EdgeInsets.all(14.gw),
  child: // Content
)

// ✅ For dialogs
Material(
  type: MaterialType.transparency,
  child: // Dialog content
)

// ❌ WRONG - Manual shadow containers
Container(
  decoration: BoxDecoration(
    boxShadow: [BoxShadow(...)], // Use ShadowBox instead
  ),
)
```

### **13. State Management in Dialogs**
```dart
// ✅ CORRECT - Use StatelessWidget when possible
class SimpleDialog extends StatelessWidget {
  final VoidCallback? onConfirm;
  // Pass callbacks for actions
}

// ✅ Use StatefulWidget only when internal state needed
class ComplexDialog extends StatefulWidget {
  // Only when dialog has internal state
}
```

## 🔄 **Animation & Interaction Rules**

### **14. User Interaction**
```dart
// ✅ Add haptic feedback for buttons
onTap: () {
  HapticFeedback.lightImpact();
  onPressed?.call();
}

// ✅ Proper navigation
Navigator.pop(context);

// ✅ Use InkWell for custom tap areas
InkWell(
  onTap: onTap,
  borderRadius: BorderRadius.circular(8.gr),
  child: // Content
)
```

## 📁 **File Organization Rules**

### **15. Directory Structure**
```
lib/shared/widgets/dialogs/     # Reusable dialogs
lib/features/*/widgets/         # Feature-specific dialogs
lib/shared/widgets/buttons/     # Button widgets
lib/shared/widgets/             # Other shared widgets
```

### **16. File Naming**
- Dialog files: `interest_coupon_dialog.dart`
- Demo files: `interest_coupon_dialog_demo.dart`
- Helper functions: Include in main dialog file

## 🧪 **Testing & Quality Rules**

### **17. Demo & Testing**
```dart
// ✅ Create demo files for complex dialogs
class MyDialogDemo extends StatelessWidget {
  // Show usage examples
}

// ✅ Create helper functions
Future<void> showMyDialog(BuildContext context, {
  required List<MyModel> data,
  VoidCallback? onAction,
}) {
  return showDialog<void>(
    context: context,
    builder: (context) => MyDialog(data: data, onAction: onAction),
  );
}
```

### **18. Error Handling**
```dart
// ✅ Handle empty states
if (items.isEmpty) {
  return Center(
    child: Column(
      children: [
        Icon(Icons.info_outline, color: context.colorTheme.textTertiary),
        Text('noDataAvailable'.tr(), style: context.textTheme.tertiary),
      ],
    ),
  );
}

// ✅ Loading states
if (isLoading) {
  return Center(child: CircularProgressIndicator());
}
```

## 🚫 **FORBIDDEN Practices**

### **19. Never Do These**
- ❌ Use `.gh` for any sizing (use `.gw` instead)
- ❌ Hardcode colors with `Color(0xFF...)`
- ❌ Hardcode font families like `'PingFang SC'`
- ❌ Create buttons without checking existing ones
- ❌ Use hardcoded text without `.tr()`
- ❌ Create dialogs without proper theme integration
- ❌ Use `Container` with manual shadows (use `ShadowBox`)
- ❌ Forget `mainAxisSize: MainAxisSize.min` in dialog columns

## ✅ **Best Practices Summary**

1. **Always use theme system** - `context.theme`, `context.colorTheme`, `context.textTheme`
2. **Always use `.gw` for sizing** - Never use `.gh`
3. **Always use `.tr()` for text** - Add to all translation files
4. **Always check existing widgets** - Don't reinvent buttons, containers, etc.
5. **Always use proper imports** - Include theme and screen_util
6. **Always create helper functions** - Make dialogs easy to use
7. **Always handle empty/loading states** - Provide good UX
8. **Always use `mainAxisSize: MainAxisSize.min`** - For dialog columns
9. **Always add haptic feedback** - For button interactions
10. **Always follow file organization** - Put files in correct directories

---

## 📋 **Checklist for New Dialogs**

- [ ] Uses `context.theme` and `context.colorTheme` for all colors
- [ ] Uses `context.textTheme` with extensions for all text
- [ ] Uses `.gw` for all sizing (never `.gh`)
- [ ] Uses `.gr` for border radius
- [ ] All text uses `.tr()` with proper translation keys
- [ ] Includes required imports (theme, screen_util, easy_localization)
- [ ] Uses existing widgets (ShadowBox, CommonButton, etc.)
- [ ] Has `mainAxisSize: MainAxisSize.min` in Column
- [ ] Includes helper function for easy usage
- [ ] Handles empty states gracefully
- [ ] Has proper file organization and naming
- [ ] Includes demo/usage examples

**Follow these rules religiously for consistent, maintainable code!**