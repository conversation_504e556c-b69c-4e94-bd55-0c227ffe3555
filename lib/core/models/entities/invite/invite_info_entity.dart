import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/invite_info_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/invite_info_entity.g.dart';

@JsonSerializable()
class InviteInfoEntity {
	double commissionRate = 0;
	double interestRate = 0;
	String inviteCode = '';
	double totalCommission = 0;

	InviteInfoEntity();

	factory InviteInfoEntity.fromJson(Map<String, dynamic> json) => $InviteInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $InviteInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}