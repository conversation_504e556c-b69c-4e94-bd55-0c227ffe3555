

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/invite/invite_info_entity.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

/// 任务相关接口
class InviteApi {

  /// 获取所有任务
  static Future<InviteInfoEntity?> fetchInfo() async {
    final res = await Http().request<InviteInfoEntity>(
      ApiEndpoints.inviteDetail,
      method: HttpMethod.get,
    );
    return res.data;
  }


}