import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/interest_coupon_model.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/interest_coupon_model.g.dart';

/// Interest coupon status enum
enum InterestCouponStatus {
  unused, // 未使用
  used, // 已使用
  expired, // 已过期
}

extension InterestCouponStatusExtension on InterestCouponStatus {
  String get displayName {
    switch (this) {
      case InterestCouponStatus.unused:
        return 'unused';
      case InterestCouponStatus.used:
        return 'used';
      case InterestCouponStatus.expired:
        return 'expired';
    }
  }

  String get translationKey {
    switch (this) {
      case InterestCouponStatus.unused:
        return 'interestCouponUnused';
      case InterestCouponStatus.used:
        return 'interestCouponUsed';
      case InterestCouponStatus.expired:
        return 'interestCouponExpired';
    }
  }
}

@JsonSerializable()
class InterestCouponModel {
  /// Coupon ID
  int id = 0;

  /// Coupon amount
  double amount = 0.0;

  /// Currency
  String currency = 'USD';

  /// Status: 1-unused, 2-used, 3-expired
  int status = 1;

  /// Valid from date (YYYY-MM-DD format)
  String validFrom = '';

  /// Valid to date (YYYY-MM-DD format)
  String validTo = '';

  /// Creation time
  String createTime = '';

  /// Usage time (if used)
  String? usageTime;

  /// Source type (same as interest records)
  int fromType = 0;

  /// Additional notes
  String? notes;

  InterestCouponModel();

  factory InterestCouponModel.fromJson(Map<String, dynamic> json) => $InterestCouponModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestCouponModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// Get coupon status enum
  InterestCouponStatus get couponStatus {
    switch (status) {
      case 1:
        return InterestCouponStatus.unused;
      case 2:
        return InterestCouponStatus.used;
      case 3:
        return InterestCouponStatus.expired;
      default:
        return InterestCouponStatus.unused;
    }
  }

  /// Check if coupon is expired based on current date
  bool get isExpired {
    if (validTo.isEmpty) return false;
    try {
      final expiryDate = DateTime.parse(validTo);
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// Get formatted validity period string
  String get validityPeriod {
    if (validFrom.isEmpty || validTo.isEmpty) return '';
    try {
      final fromDate = DateTime.parse(validFrom);
      final toDate = DateTime.parse(validTo);
      return '${fromDate.year}年${fromDate.month}月${fromDate.day}日-${toDate.year}年${toDate.month}月${toDate.day}日';
    } catch (e) {
      return '$validFrom-$validTo';
    }
  }
}

@JsonSerializable()
class InterestCouponListModel {
  int current = 0;
  bool hasNext = false;
  List<InterestCouponModel> records = [];
  int total = 0;

  InterestCouponListModel();

  factory InterestCouponListModel.fromJson(Map<String, dynamic> json) => $InterestCouponListModelFromJson(json);

  Map<String, dynamic> toJson() => $InterestCouponListModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// Get unused coupons
  List<InterestCouponModel> get unusedCoupons {
    return records.where((coupon) => coupon.couponStatus == InterestCouponStatus.unused).toList();
  }

  /// Get used and expired coupons
  List<InterestCouponModel> get usedAndExpiredCoupons {
    return records
        .where((coupon) =>
            coupon.couponStatus == InterestCouponStatus.used || coupon.couponStatus == InterestCouponStatus.expired)
        .toList();
  }
}
