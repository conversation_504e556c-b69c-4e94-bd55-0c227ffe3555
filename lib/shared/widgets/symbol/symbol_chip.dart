import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class SymbolChip extends StatelessWidget {
  final String name;
  final Color? chipColor;
  const SymbolChip({super.key, required this.name, this.chipColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 15.gw,
      padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 2.gw),
      decoration: BoxDecoration(
        color: chipColor ?? context.theme.primaryColor,
        borderRadius: BorderRadius.circular(2.gr),
      ),
      child: Text(marketName, style: context.textTheme.secondary.w600.fs8),
    );
  }

  String get marketName {
    return switch (name) {
      'SZSE' => 'SZ',
      'SSE' => 'SH',
      'HKEX' => 'HK',
      'US' => 'US',
      _ => name,
    };
  }
}
