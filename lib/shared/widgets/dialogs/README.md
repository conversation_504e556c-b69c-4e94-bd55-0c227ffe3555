# Interest Coupon Dialog

This directory contains the Interest Coupon Dialog implementation based on the Figma design.

## Files

- `interest_coupon_dialog.dart` - Main dialog widget implementation
- `interest_coupon_dialog_demo.dart` - Demo/test file showing usage examples
- `README.md` - This documentation file

## Features

### Dialog Features
- **Single View Layout**: Shows all coupons in a single scrollable list (matching Figma design)
- **Coupon Items**: Each coupon displays amount, status, and validity period in a horizontal wrap layout
- **Status Indicators**: Color-coded status text (green for unused, blue for used, red for expired)
- **Use Button**: Centered "使用" button at the bottom for coupon usage
- **Dividers**: Subtle dividers between coupon items and sections
- **Exact Figma Match**: Pixel-perfect implementation matching the provided design
- **Fixed Dimensions**: 347x336 container size as per Figma specifications

### Data Model
- `InterestCouponModel` - Individual coupon data
- `InterestCouponListModel` - List of coupons with pagination support
- `InterestCouponStatus` - Enum for coupon status (unused, used, expired)

## Usage

### Basic Usage

```dart
import 'package:gp_stock_app/shared/widgets/dialogs/interest_coupon_dialog.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';

// Show the dialog
showInterestCouponDialog(
  context,
  coupons: userCoupons,
  onUsePressed: () {
    Navigator.of(context).pop();
    // Handle use button press
  },
  onCouponTap: (coupon) {
    // Handle coupon tap
  },
);
```

### From Notification

```dart
// Example usage from notification handler
NotificationHandler.handleInterestCouponNotification(context);
```

## Translations

The following translation keys have been added to all language files:

- `interestCoupon` - "利息券" / "Interest Coupon"
- `interestCouponUnused` - "未使用" / "Unused"
- `interestCouponUsed` - "已使用" / "Used"
- `interestCouponExpired` - "已过期" / "Expired"
- `interestCouponUsedExpired` - "已使用/过期" / "Used/Expired"
- `historyRecords` - "历史记录" / "History Records"
- `noCouponsAvailable` - "暂无可用券" / "No coupons available"
- `validityPeriod` - "有效期" / "Validity Period"

## Theme Integration

The dialog uses the existing theme system:
- `context.theme` for basic theme colors
- `context.colorTheme` for custom app colors
- `context.textTheme` for text styles
- `.gw` for responsive sizing (never `.gh` as per project guidelines)
- `ShadowBox` for card containers
- `CommonButton` for buttons
- `CommonTabBar` for tab navigation

## Design Specifications

Exact implementation of the Figma design:
- Modal dialog with rounded corners (12px)
- Fixed dimensions: 347x336px
- White background (#FFFFFF)
- Title "利息券" centered at top
- Horizontal divider line (#DEDEDE, 0.45px)
- Coupon items with 8px spacing in wrap layout
- Color-coded status text:
  - Unused: #1CB570 (green)
  - Used: #4062D8 (blue)
  - Expired: #DF4446 (red)
- Validity period text: #8897B8 (light gray)
- "使用" button positioned at bottom center (32x22px)
- PingFang SC font family throughout

## Future Enhancements

- API integration for fetching real coupon data
- Coupon usage functionality
- Pull-to-refresh support
- Pagination for large coupon lists
- Search and filter capabilities
- Animation improvements