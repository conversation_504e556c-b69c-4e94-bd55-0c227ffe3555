# Interest Coupon Dialog

This directory contains the Interest Coupon Dialog implementation based on the Figma design.

## Files

- `interest_coupon_dialog.dart` - Main dialog widget implementation
- `interest_coupon_dialog_demo.dart` - Demo/test file showing usage examples
- `README.md` - This documentation file

## Features

### Dialog Features
- **Tabbed Interface**: Two tabs for "Unused" and "Used/Expired" coupons
- **Coupon Cards**: Each coupon displays amount, status, and validity period
- **Status Indicators**: Color-coded status chips (green for unused, blue for used, red for expired)
- **History Button**: Button to navigate to full coupon history
- **Responsive Design**: Adapts to different screen sizes
- **Theme Integration**: Uses existing app theme colors and text styles

### Data Model
- `InterestCouponModel` - Individual coupon data
- `InterestCouponListModel` - List of coupons with pagination support
- `InterestCouponStatus` - Enum for coupon status (unused, used, expired)

## Usage

### Basic Usage

```dart
import 'package:gp_stock_app/shared/widgets/dialogs/interest_coupon_dialog.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';

// Show the dialog
showInterestCouponDialog(
  context,
  coupons: userCoupons,
  onHistoryPressed: () {
    Navigator.of(context).pop();
    // Navigate to history screen
  },
  onCouponTap: (coupon) {
    // Handle coupon tap
  },
);
```

### From Notification

```dart
// Example usage from notification handler
NotificationHandler.handleInterestCouponNotification(context);
```

## Translations

The following translation keys have been added to all language files:

- `interestCoupon` - "利息券" / "Interest Coupon"
- `interestCouponUnused` - "未使用" / "Unused"
- `interestCouponUsed` - "已使用" / "Used"
- `interestCouponExpired` - "已过期" / "Expired"
- `interestCouponUsedExpired` - "已使用/过期" / "Used/Expired"
- `historyRecords` - "历史记录" / "History Records"
- `noCouponsAvailable` - "暂无可用券" / "No coupons available"
- `validityPeriod` - "有效期" / "Validity Period"

## Theme Integration

The dialog uses the existing theme system:
- `context.theme` for basic theme colors
- `context.colorTheme` for custom app colors
- `context.textTheme` for text styles
- `.gw` for responsive sizing (never `.gh` as per project guidelines)
- `ShadowBox` for card containers
- `CommonButton` for buttons
- `CommonTabBar` for tab navigation

## Design Specifications

Based on the Figma design:
- Modal dialog with rounded corners (12px)
- Two-tab interface at the top
- Coupon cards with amount, status, and validity period
- Color-coded status indicators
- History button at the bottom
- Professional and clean design

## Future Enhancements

- API integration for fetching real coupon data
- Coupon usage functionality
- Pull-to-refresh support
- Pagination for large coupon lists
- Search and filter capabilities
- Animation improvements