import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';

/// Interest Coupon Dialog Widget
/// Shows a modal dialog exactly matching the Figma design
class InterestCouponDialog extends StatelessWidget {
  final List<InterestCouponModel> coupons;
  final VoidCallback? onUsePressed;
  final Function(InterestCouponModel)? onCouponTap;

  const InterestCouponDialog({
    super.key,
    required this.coupons,
    this.onUsePressed,
    this.onCouponTap,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gr),
      ),
      child: Container(
        width: 347.gw,
        height: 336.gw,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(12.gr),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                _buildHeader(context),
                _buildDivider(context),
                Expanded(child: _buildCouponList(context)),
                SizedBox(height: 65.gw), // Space for the button
              ],
            ),
            _buildUseButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 12.gw),
      child: Text(
        'interestCoupon'.tr(),
        style: context.textTheme.title,
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 34.gw, left: 16.gw, right: 16.gw),
      height: 0.45.gw,
      color: context.theme.dividerColor,
    );
  }

  Widget _buildCouponList(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      child: Column(
        children: [
          SizedBox(height: 12.gw),
          ...coupons.asMap().entries.map((entry) {
            final index = entry.key;
            final coupon = entry.value;
            return Column(
              children: [
                _buildCouponItem(context, coupon),
                if (index < coupons.length - 1) _buildItemDivider(context),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildItemDivider(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.gw),
      height: 0.45.gw,
      color: context.theme.dividerColor,
    );
  }

  Widget _buildCouponItem(BuildContext context, InterestCouponModel coupon) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.gw),
      child: Wrap(
        spacing: 8.gw,
        runSpacing: 8.gw,
        children: [
          Text(
            '${'interestCoupon'.tr()}¥${coupon.amount.toStringAsFixed(0)}',
            style: context.textTheme.title,
          ),
          Text(
            coupon.couponStatus == InterestCouponStatus.unused
                ? 'interestCouponUnused'.tr()
                : coupon.couponStatus == InterestCouponStatus.used
                    ? 'interestCouponUsed'.tr()
                    : 'interestCouponExpired'.tr(),
            style: context.textTheme.title.copyWith(
              color: coupon.couponStatus == InterestCouponStatus.unused
                  ? context.colorTheme.stockGreen
                  : coupon.couponStatus == InterestCouponStatus.used
                      ? context.theme.primaryColor
                      : context.colorTheme.stockRed,
            ),
          ),
          Text(
            '${'validityPeriod'.tr()}：${coupon.validityPeriod}',
            style: context.textTheme.tertiary,
          ),
        ],
      ),
    );
  }

  Widget _buildUseButton(BuildContext context) {
    return Positioned(
      left: 158.03.gw,
      bottom: 43.gw,
      child: GestureDetector(
        onTap: onUsePressed,
        child: Container(
          width: 32.gw,
          height: 22.gw,
          decoration: BoxDecoration(
            color: context.theme.primaryColor,
            borderRadius: BorderRadius.circular(4.gr),
          ),
          alignment: Alignment.center,
          child: Text(
            'use'.tr(),
            style: context.textTheme.primary.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// Helper function to show the Interest Coupon Dialog
Future<void> showInterestCouponDialog(
  BuildContext context, {
  required List<InterestCouponModel> coupons,
  VoidCallback? onUsePressed,
  Function(InterestCouponModel)? onCouponTap,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return InterestCouponDialog(
        coupons: coupons,
        onUsePressed: onUsePressed,
        onCouponTap: onCouponTap,
      );
    },
  );
}
