import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

/// Interest Coupon Dialog Widget
/// Shows a modal dialog with tabbed interface for managing interest coupons
class InterestCouponDialog extends StatefulWidget {
  final List<InterestCouponModel> coupons;
  final VoidCallback? onHistoryPressed;
  final Function(InterestCouponModel)? onCouponTap;

  const InterestCouponDialog({
    super.key,
    required this.coupons,
    this.onHistoryPressed,
    this.onCouponTap,
  });

  @override
  State<InterestCouponDialog> createState() => _InterestCouponDialogState();
}

class _InterestCouponDialogState extends State<InterestCouponDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          // Tab index changed
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 40.gw),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: context.theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildTabBar(),
            Flexible(
              child: _buildTabContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.gw),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'interestCoupon'.tr(),
            style: context.textTheme.title.fs16.w600,
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Icon(
              Icons.close,
              size: 24.gw,
              color: context.colorTheme.textRegular,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gw),
      child: CommonTabBar(
        data: ['interestCouponUnused'.tr(), 'interestCouponUsedExpired'.tr()],
        onTap: (index) {
          _tabController.animateTo(index);
        },
        height: 40.gw,
        radius: 8.gw,
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      padding: EdgeInsets.all(16.gw),
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildCouponList(_getUnusedCoupons()),
          _buildCouponList(_getUsedAndExpiredCoupons()),
        ],
      ),
    );
  }

  Widget _buildCouponList(List<InterestCouponModel> coupons) {
    if (coupons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_outlined,
              size: 48.gw,
              color: context.colorTheme.textTertiary,
            ),
            SizedBox(height: 16.gw),
            Text(
              'noCouponsAvailable'.tr(),
              style: context.textTheme.regular,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: coupons.length,
      itemBuilder: (context, index) {
        return _buildCouponCard(coupons[index]);
      },
    );
  }

  Widget _buildCouponCard(InterestCouponModel coupon) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.gw),
      child: ShadowBox(
        borderRadius: BorderRadius.circular(8.gw),
        padding: EdgeInsets.all(14.gw),
        child: GestureDetector(
          onTap: () => widget.onCouponTap?.call(coupon),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${'interestCoupon'.tr()}¥${coupon.amount.toStringAsFixed(0)}',
                    style: context.textTheme.title.w600,
                  ),
                  _buildStatusChip(coupon.couponStatus),
                ],
              ),
              SizedBox(height: 8.gw),
              Text(
                '${'validityPeriod'.tr()}：${coupon.validityPeriod}',
                style: context.textTheme.tertiary.fs12,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(InterestCouponStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case InterestCouponStatus.unused:
        backgroundColor = context.colorTheme.stockGreen.withValues(alpha: 0.1);
        textColor = context.colorTheme.stockGreen;
        text = 'interestCouponUnused'.tr();
        break;
      case InterestCouponStatus.used:
        backgroundColor = context.theme.primaryColor.withValues(alpha: 0.1);
        textColor = context.theme.primaryColor;
        text = 'interestCouponUsed'.tr();
        break;
      case InterestCouponStatus.expired:
        backgroundColor = context.colorTheme.stockRed.withValues(alpha: 0.1);
        textColor = context.colorTheme.stockRed;
        text = 'interestCouponExpired'.tr();
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4.gw),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4.gw),
      ),
      child: Text(
        text,
        style: context.textTheme.regular.fs12.copyWith(color: textColor),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: EdgeInsets.all(16.gw),
      child: CommonButton(
        title: 'historyRecords'.tr(),
        style: CommonButtonStyle.outlined,
        onPressed: widget.onHistoryPressed,
        height: 44.gw,
      ),
    );
  }

  List<InterestCouponModel> _getUnusedCoupons() {
    return widget.coupons.where((coupon) => coupon.couponStatus == InterestCouponStatus.unused).toList();
  }

  List<InterestCouponModel> _getUsedAndExpiredCoupons() {
    return widget.coupons
        .where((coupon) =>
            coupon.couponStatus == InterestCouponStatus.used || coupon.couponStatus == InterestCouponStatus.expired)
        .toList();
  }
}

/// Helper function to show the Interest Coupon Dialog
Future<void> showInterestCouponDialog(
  BuildContext context, {
  required List<InterestCouponModel> coupons,
  VoidCallback? onHistoryPressed,
  Function(InterestCouponModel)? onCouponTap,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return InterestCouponDialog(
        coupons: coupons,
        onHistoryPressed: onHistoryPressed,
        onCouponTap: onCouponTap,
      );
    },
  );
}
