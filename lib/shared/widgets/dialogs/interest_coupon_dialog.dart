import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';

/// Interest Coupon Dialog Widget
/// Shows a modal dialog exactly matching the Figma design
class InterestCouponDialog extends StatelessWidget {
  final List<InterestCouponModel> coupons;
  final VoidCallback? onUsePressed;
  final Function(InterestCouponModel)? onCouponTap;

  const InterestCouponDialog({
    super.key,
    required this.coupons,
    this.onUsePressed,
    this.onCouponTap,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 20.gw, vertical: 40.gw),
      child: Container(
        width: 347.gw,
        height: 336.gw,
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(12.gw),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                _buildHeader(context),
                _buildDivider(),
                Expanded(child: _buildCouponList(context)),
                SizedBox(height: 65.gw), // Space for the button
              ],
            ),
            _buildUseButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 12.gw),
      child: Text(
        'interestCoupon'.tr(),
        style: const TextStyle(
          fontFamily: 'PingFang SC',
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Color(0xFF525A79),
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.only(top: 34.gw, left: 16.gw, right: 16.gw),
      height: 0.45.gw,
      color: const Color(0xFFDEDEDE),
    );
  }

  Widget _buildCouponList(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      child: Column(
        children: [
          SizedBox(height: 12.gw),
          ...coupons.asMap().entries.map((entry) {
            final index = entry.key;
            final coupon = entry.value;
            return Column(
              children: [
                _buildCouponItem(context, coupon),
                if (index < coupons.length - 1) _buildItemDivider(),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildItemDivider() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.gw),
      height: 0.45.gw,
      color: const Color(0xFFDEDEDE),
    );
  }

  Widget _buildCouponItem(BuildContext context, InterestCouponModel coupon) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.gw),
      child: Wrap(
        spacing: 8.gw,
        runSpacing: 8.gw,
        children: [
          Text(
            '${'interestCoupon'.tr()}¥${coupon.amount.toStringAsFixed(0)}',
            style: const TextStyle(
              fontFamily: 'PingFang SC',
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Color(0xFF525A79),
              height: 1.4,
            ),
          ),
          Text(
            coupon.couponStatus == InterestCouponStatus.unused
                ? 'interestCouponUnused'.tr()
                : coupon.couponStatus == InterestCouponStatus.used
                    ? 'interestCouponUsed'.tr()
                    : 'interestCouponExpired'.tr(),
            style: TextStyle(
              fontFamily: 'PingFang SC',
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: coupon.couponStatus == InterestCouponStatus.unused
                  ? const Color(0xFF1CB570)
                  : coupon.couponStatus == InterestCouponStatus.used
                      ? const Color(0xFF4062D8)
                      : const Color(0xFFDF4446),
              height: 1.4,
            ),
          ),
          Text(
            '${'validityPeriod'.tr()}：${coupon.validityPeriod}',
            style: const TextStyle(
              fontFamily: 'PingFang SC',
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Color(0xFF8897B8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUseButton(BuildContext context) {
    return Positioned(
      left: 158.03.gw,
      bottom: 43.gw, // 336 - 271 - 22 = 43
      child: GestureDetector(
        onTap: onUsePressed,
        child: Container(
          width: 32.gw,
          height: 22.gw,
          decoration: BoxDecoration(
            color: const Color(0xFF4062D8), // Primary blue color
            borderRadius: BorderRadius.circular(4.gw),
          ),
          alignment: Alignment.center,
          child: const Text(
            '使用',
            style: TextStyle(
              fontFamily: 'PingFang SC',
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Color(0xFFFFFFFF),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// Helper function to show the Interest Coupon Dialog
Future<void> showInterestCouponDialog(
  BuildContext context, {
  required List<InterestCouponModel> coupons,
  VoidCallback? onUsePressed,
  Function(InterestCouponModel)? onCouponTap,
}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return InterestCouponDialog(
        coupons: coupons,
        onUsePressed: onUsePressed,
        onCouponTap: onCouponTap,
      );
    },
  );
}
