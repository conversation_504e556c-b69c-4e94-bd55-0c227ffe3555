import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';
import 'package:gp_stock_app/shared/widgets/dialogs/interest_coupon_dialog.dart';

/// Demo page to test the Interest Coupon Dialog
/// This file demonstrates how to use the InterestCouponDialog widget
class InterestCouponDialogDemo extends StatelessWidget {
  const InterestCouponDialogDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Interest Coupon Dialog Demo'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _showInterestCouponDialog(context),
          child: const Text('Show Interest Coupon Dialog'),
        ),
      ),
    );
  }

  void _showInterestCouponDialog(BuildContext context) {
    // Create sample coupon data
    final List<InterestCouponModel> sampleCoupons = [
      InterestCouponModel()
        ..id = 1
        ..amount = 18.0
        ..currency = 'USD'
        ..status = 1 // unused
        ..validFrom = '2025-05-05'
        ..validTo = '2025-10-02'
        ..createTime = '2025-01-10 10:00:00'
        ..fromType = 2, // sign-in bonus
      InterestCouponModel()
        ..id = 2
        ..amount = 25.0
        ..currency = 'USD'
        ..status = 1 // unused
        ..validFrom = '2025-01-01'
        ..validTo = '2025-12-31'
        ..createTime = '2025-01-05 14:30:00'
        ..fromType = 3, // activity bonus
      InterestCouponModel()
        ..id = 3
        ..amount = 10.0
        ..currency = 'USD'
        ..status = 2 // used
        ..validFrom = '2024-12-01'
        ..validTo = '2025-06-01'
        ..createTime = '2024-12-01 09:15:00'
        ..usageTime = '2025-01-08 16:45:00'
        ..fromType = 1, // manual addition
      InterestCouponModel()
        ..id = 4
        ..amount = 15.0
        ..currency = 'USD'
        ..status = 3 // expired
        ..validFrom = '2024-06-01'
        ..validTo = '2024-12-31'
        ..createTime = '2024-06-01 11:20:00'
        ..fromType = 6, // deposit bonus
    ];

    showInterestCouponDialog(
      context,
      coupons: sampleCoupons,
      onHistoryPressed: () {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('History button pressed')),
        );
      },
      onCouponTap: (coupon) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Coupon tapped: \$${coupon.amount}')),
        );
      },
    );
  }
}

/// Example of how to call the dialog from a notification or other context
class NotificationHandler {
  static void handleInterestCouponNotification(BuildContext context) {
    // This would typically fetch real data from an API
    final List<InterestCouponModel> coupons = _fetchUserCoupons();

    showInterestCouponDialog(
      context,
      coupons: coupons,
      onHistoryPressed: () {
        Navigator.of(context).pop();
        // Navigate to interest records screen
        // Navigator.pushNamed(context, '/interest-records');
      },
      onCouponTap: (coupon) {
        // Handle coupon usage or show details
        _handleCouponUsage(context, coupon);
      },
    );
  }

  static List<InterestCouponModel> _fetchUserCoupons() {
    // In a real app, this would be an API call
    // For demo purposes, return empty list
    return [];
  }

  static void _handleCouponUsage(BuildContext context, InterestCouponModel coupon) {
    if (coupon.couponStatus == InterestCouponStatus.unused) {
      // Show usage confirmation dialog or navigate to usage screen
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Use Coupon'),
          content: Text('Do you want to use this \$${coupon.amount} coupon?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Handle coupon usage logic here
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Coupon \$${coupon.amount} used successfully')),
                );
              },
              child: const Text('Use'),
            ),
          ],
        ),
      );
    }
  }
}
