import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/widgets/profile/profile_service.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';

import '../logic/auth_n/auth_n_cubit.dart';
import '../logic/profile/profile_cubit.dart';
import '../widgets/profile/profile_action_button.dart';
import '../widgets/profile/profile_header.dart';
// Import styleGP widgets for kGP template
import '../widgets/profile/styleGP/profile_action_button.dart' as style_gp;
import '../widgets/profile/styleGP/profile_header.dart' as style_gp;
import '../widgets/profile/styleGP/profile_service.dart' as style_gp;

class ProfileScreen extends StatelessWidget {
  final AppSkinStyle? skinStyle;

  const ProfileScreen({super.key, this.skinStyle});

  @override
  Widget build(BuildContext context) {
    // Determine which skin style to use
    final currentSkinStyle = skinStyle ?? AppConfig.instance.skinStyle;


    return Scaffold(
      extendBodyBehindAppBar: true,
      body: RefreshIndicator.adaptive(
        backgroundColor: context.theme.cardColor,
        onRefresh: () async {
          await Future.wait([
            context.read<AccountInfoCubit>().getAccountInfo(),
            context.read<ProfileCubit>().getUserInfo(),
            context.read<AuthNCubit>().getAuthNInfo(),
          ]);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: AnimationLimiter(
            child: Column(
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 600),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: widget,
                  ),
                ),
                children: _buildProfileWidgets(currentSkinStyle),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build profile widgets based on skin style
  List<Widget> _buildProfileWidgets(AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        // Use original GP style widgets
        return [
          style_gp.ProfileHeader(),
          12.verticalSpace,
          style_gp.ProfileServiceConsult(),
          12.verticalSpace,
          style_gp.ProfileActionButton(),
        ];
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        // Use new template style widgets
        return [
          ProfileHeader(),
          12.verticalSpace,
          ProfileServiceConsult(),
          12.verticalSpace,
          ProfileActionButton(),
        ];
    }
  }
}
