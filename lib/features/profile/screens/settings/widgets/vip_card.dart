import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../../shared/constants/assets.dart';
import '../../../../../shared/constants/enums.dart';
import '../../../../sign_in/logic/sign_in/sign_in_cubit.dart';
import '../../../domain/models/vip/vip_config_model.dart';
import '../../../logic/profile/profile_cubit.dart';
import '../../../logic/vip/vip_cubit.dart';
import '../../../logic/vip/vip_state.dart';

class VipCard extends StatelessWidget {
  const VipCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      //background image
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.vipCardBackground),
          fit: BoxFit.fill,
        ),
      ),

      child: BlocBuilder<SignInCubit, SignInState>(
        builder: (context, state) {
          final userData = getIt<UserCubit>().state.userInfo;
          return BlocBuilder<VipCubit, VipState>(
            buildWhen: (previous, current) =>
                previous.vipConfigStatus != current.vipConfigStatus || previous.vipConfig != current.vipConfig,
            builder: (context, vipState) {
              if (vipState.vipConfigStatus == DataStatus.loading) {
                return _buildShimmerContent(context);
              }

              return Column(
                children: [
                  _buildUserInfo(context, userData, vipState.vipConfig?.level ?? 0),
                  const SizedBox(height: 16),
                  _buildVipBenefits(context, vipState.vipConfig),
                ],
              );
            },
          );
        },
      ),
    );
  }

  /// Builds the user info section with avatar, nickname, mobile/email, and VIP icon.
  Widget _buildUserInfo(BuildContext context, UserModel? userData, int vipLevel) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildAvatar(context),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userData?.nickname ?? '',
                style: context.textTheme.secondary.w500,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                userData?.mobile ?? userData?.email ?? '',
                style: context.textTheme.secondary.fs12,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Container(
          width: 60.gw,
          height: 30.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.textTertiary,
            borderRadius: BorderRadius.circular(4.gr),
          ),
          padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 2.gw),
          alignment: Alignment.center,
          child: Text(
            'VIP $vipLevel',
            style: context.textTheme.secondary.fs18.w800,
          ),
        ),
      ],
    );
  }

  /// Builds the avatar with loading state or default icon.
  Widget _buildAvatar(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) =>
          previous.userData?.avatar != current.userData?.avatar || previous.updateStatus != current.updateStatus,
      builder: (context, state) {
        final isLoading = state.updateStatus == DataStatus.loading && state.updatingField == ProfileUpdateField.avatar;

        return Row(
          children: [
            if (isLoading)
              SizedBox(
                width: 64.gw,
                height: 64.gh,
                child: Center(
                  child: SizedBox(
                    width: 30.gw,
                    height: 30.gh,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        context.theme.primaryColor,
                      ),
                    ),
                  ),
                ),
              )
            else
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  borderRadius: BorderRadius.circular(40.gr),
                ),
                child: CircleAvatar(
                  radius: 30.5.gr,
                  backgroundColor: context.theme.primaryColor,
                  backgroundImage: state.userData?.avatar != null
                      ? AssetImage('assets/images/avatars/${state.userData!.avatar}.png')
                      : null,
                  child: state.userData?.avatar == null
                      ? Icon(
                          Icons.person_outline,
                          color: Colors.white,
                          size: 30.gw,
                        )
                      : null,
                ),
              ),
            13.horizontalSpace,
          ],
        );
      },
    );
  }

  /// Builds the VIP benefits section (interest discount, commission rate, etc.).
  Widget _buildVipBenefits(BuildContext context, VipConfigModel? vipConfig) {
    final isEnglish = context.locale.languageCode == 'en';

    if (isEnglish) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBenefitRow(
            context,
            label: 'interestDiscount'.tr(),
            value: '${vipConfig?.interestDiscount ?? 0}${'percent'.tr()}',
          ),
          const SizedBox(height: 8),
          _buildBenefitRow(
            context,
            label: 'commissionRate'.tr(),
            value: '${vipConfig?.commissionRate ?? 0}${'percent'.tr()}',
          ),
          const SizedBox(height: 8),
          _buildBenefitRow(
            context,
            label: 'interestRefundRate'.tr(),
            value: '${vipConfig?.interestRate ?? 0}${'percent'.tr()}',
          ),
          const SizedBox(height: 8),
          _buildBenefitRow(
            context,
            label: 'graduallyRate'.tr(),
            value: '${vipConfig?.graduallyRate ?? 0}${'percent'.tr()}',
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'interestDiscount'.tr(),
                style: context.textTheme.secondary,
              ),
              Text(
                '${vipConfig?.interestDiscount ?? 0}${'percent'.tr()}',
                style: context.textTheme.secondary.w800,
              ),
              Text(
                'commissionRate'.tr(),
                style: context.textTheme.secondary,
              ),
              Text(
                '${vipConfig?.commissionRate ?? 0}${'percent'.tr()}',
                style: context.textTheme.secondary.w800,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'interestRefundRate'.tr(),
                style: context.textTheme.secondary,
              ),
              Text(
                '${vipConfig?.interestRate ?? 0}${'percent'.tr()}',
                style: context.textTheme.secondary.w800,
              ),
              Text(
                'graduallyRate'.tr(),
                style: context.textTheme.secondary,
              ),
              Text(
                '${vipConfig?.graduallyRate ?? 0}${'percent'.tr()}',
                style: context.textTheme.secondary.w800,
              ),
            ],
          ),
        ],
      );
    }
  }

  /// Builds a single benefit row with label and value for English layout.
  Widget _buildBenefitRow(BuildContext context, {required String label, required String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textTheme.secondary,
        ),
        Text(
          value,
          style: context.textTheme.secondary.w800,
        ),
      ],
    );
  }

  /// Builds shimmer loading content for the VIP card
  Widget _buildShimmerContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerWidget(
              child: CircleAvatar(
                radius: 30.5.gr,
                backgroundColor: Colors.grey[300],
              ),
            ),
            13.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerWidget(
                    child: Container(
                      width: 150.gw,
                      height: 20.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                  ),
                  8.verticalSpace,
                  ShimmerWidget(
                    child: Container(
                      width: 120.gw,
                      height: 14.gh,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.gr),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ShimmerWidget(
              child: Container(
                width: 40.gw,
                height: 40.gh,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.gr),
                ),
              ),
            ),
          ],
        ),
        16.verticalSpace,
        Column(
          children: List.generate(
            4,
            (index) => Column(
              children: [
                ShimmerWidget(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 120.gw,
                        height: 14.gh,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4.gr),
                        ),
                      ),
                      Container(
                        width: 80.gw,
                        height: 14.gh,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4.gr),
                        ),
                      ),
                    ],
                  ),
                ),
                if (index < 3) 8.verticalSpace,
              ],
            ),
          ),
        ),
      ],
    );
  }
}
