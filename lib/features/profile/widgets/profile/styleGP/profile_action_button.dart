import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/common_dialog.dart';

import '../../../../../shared/constants/assets.dart';
import '../../../../../shared/routes/app_router.dart';
import 'profile_widget.dart';

class ProfileActionButton extends StatelessWidget {
  const ProfileActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.gr),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gr),
        color: context.theme.cardColor,
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gh),
      child: AnimationLimiter(
        child: Column(
          spacing: 20.gh,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.vipIcon,
                      title: 'vip'.tr(),
                      onTap: () => AuthUtils.verifyAuth(
                          () => getIt<NavigatorService>().push(AppRouter.routeMissionCenter, arguments: true)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.missionIcon,
                      title: 'missionCenter'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeMissionCenter)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.withdrawalIcon,
                      title: 'depositWithdrawal'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => _showActionDialog(context)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.thirdPartyIcon,
                      title: 'third'.tr(),
                      onTap: () => AuthUtils.verifyAuth(
                        () => getIt<NavigatorService>().push(AppRouter.routeDepositMain, arguments: {
                          "type": DepositType.third,
                        }),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.invitationIcon,
                      title: 'invitationRebate'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeInvite)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.questionIcon,
                      title: 'newbieQuestions'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeQuestions)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.authIcon,
                      title: 'realNameAuthentication'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAuthN)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.settingsIcon,
                      title: 'systemSettings'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeSettings)),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.infoIcon,
                      title: 'aboutUs'.tr(),
                      onTap: () => getIt<NavigatorService>().push(AppRouter.routeAboutUs),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.exchangeIcon,
                      title: 'exchange'.tr(),
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeConvertRate)),
                    ),
                  ),
                  Expanded(
                    child: BuildActionButton(
                      icon: Assets.selectLineIconSvg,
                      title: 'line_selection'.tr(),
                      onTap: () => getIt<NavigatorService>().push(AppRouter.routeUSDTWalletManage),
                      // onTap: () => getIt<NavigatorService>().push(AppRouter.routeSelectLine),
                      
                    ),
                  ),
                  Expanded(
                    child: BlocBuilder<UserCubit, UserState>(
                      builder: (context, state) {
                        return Visibility(
                          visible: state.isLogin,
                          child: BuildActionButton(
                            icon: Assets.logoutIcon,
                            title: 'logout'.tr(),
                            onTap: () {
                              CommonDialog(
                                context,
                                title: "confirm_logout".tr(),
                                sureBtnTitleStyle: context.textTheme.stockRed,
                                complete: () {
                                  Helper.logoutUser(isNavToMain: true);
                                },
                              ).show();
                            },
                            customColor: Colors.red,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<void> _showActionDialog(BuildContext context) async {
  final result = await showDialog<String>(
    context: context,
    builder: (ctx) => ScaleTransition(
      scale: Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(
          parent: ModalRoute.of(ctx)!.animation!,
          curve: Curves.easeInOut,
        ),
      ),
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.gr),
        ),
        child: Container(
          padding: EdgeInsets.all(16.gr),
          decoration: BoxDecoration(
            color: ctx.theme.cardColor,
            borderRadius: BorderRadius.circular(16.gr),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'depositWithdrawal'.tr(),
                    style: ctx.textTheme.primary.fs18.w600.copyWith(
                      color: ctx.theme.primaryColor,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(ctx),
                    child: Container(
                      padding: EdgeInsets.all(4.gr),
                      child: Icon(
                        Icons.close,
                        size: 24.gr,
                        color: ctx.theme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              24.verticalSpace,
              IntrinsicHeight(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ActionOptionButton(
                      icon: Assets.myAssetIcon,
                      title: 'topUpDeposit'.tr(),
                      onTap: () => Navigator.pop(ctx, 'deposit'),
                    ),
                    ActionOptionButton(
                      icon: Assets.withdrawalIcon,
                      title: 'cashOut'.tr(),
                      onTap: () => Navigator.pop(ctx, 'withdraw'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );

  if (result == 'deposit' && context.mounted) {
    AuthUtils.verifyAuth(
      () => getIt<NavigatorService>().push(AppRouter.routeDepositMain),
    );
  } else if (result == 'withdraw' && context.mounted) {
    AuthUtils.verifyAuth(
      () => context.verifyRealName(
        () => getIt<NavigatorService>().push(AppRouter.routeWithdrawMain),
      ),
    );
  }
}
