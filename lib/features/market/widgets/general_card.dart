import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/models/stock/stock_response.dart';
import '../../../shared/routes/app_router.dart';
import '../domain/models/plate_info_request/plate_info_request.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class MarketGeneralCard extends StatelessWidget {
  final StockItem? data;
  final TodaysTab tab;

  // Standard spacing
  static const double _padding = 10.0;
  static const double _verticalSpacing = 4.0;

  const MarketGeneralCard({
    required this.data,
    required this.tab,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (data == null) return const SizedBox.shrink();

    final title = data?.name ?? "N/A";
    final percentage = (data!.gain ?? 0.0).toStringAsFixed(2);
    final price = '${data?.latestPrice ?? 0.0}';
    final leadupGain = ((data!.leadupGain ?? 0.0) * 100).toStringAsFixed(2);
    final markets = _getMarketsByTab();

    return GestureDetector(
      onTap: () => _navigateToPlateInfo(context, markets),
      child: Container(
        padding: const EdgeInsets.all(_padding),
        decoration: _buildCardDecoration(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildTitleText(context, title),
            const SizedBox(height: _verticalSpacing),
            _buildPercentageText(context, percentage),
            const SizedBox(height: _verticalSpacing),
            _buildSymbolText(context, data?.nameAlt ?? ''),
            const SizedBox(height: _verticalSpacing),
            _buildPriceAndGainRow(context, price, leadupGain),
          ],
        ),
      ),
    );
  }

  String _getMarketsByTab() {
    return switch (tab) {
      TodaysTab.aShares => 'SZSE,SSE',
      TodaysTab.hkShares => 'HKEX',
      TodaysTab.usShares => 'US',
    };
  }

  void _navigateToPlateInfo(BuildContext context, String markets) {
    AuthUtils.verifyAuth(() {
      getIt<NavigatorService>().push(AppRouter.routePlateInfo,
        arguments: PlateInfoRequest(
          plateId: data?.industryPlate ?? '',
          markets: markets,
          securityType: data?.securityType ?? '',
          title: data?.name ?? '',
        ),
      );
    });
  }

  BoxDecoration _buildCardDecoration(BuildContext context) {
    return BoxDecoration(
      color: context.theme.cardColor,
      border: Border.all(
        color: context.theme.dividerColor,
        width: 0.2,
      ),
      borderRadius: BorderRadius.circular(4.gr),
    );
  }

  Widget _buildTitleText(BuildContext context, String title) {
    return Text(
      title,
      style: context.textTheme.primary.w500,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      textAlign: TextAlign.center,
    );
  }

  Widget _buildPercentageText(BuildContext context, String percentage) {
    return FlipText(
      // '$percentage %',÷
      double.parse(percentage),
      fractionDigits: 2,
      suffix: ' %',
      prefix: TradingUtils.getSign(double.parse(percentage)),
      style: context.textTheme.primary.fs12.w700.ffAkz.copyWith(
        color: (data?.gain ?? 0.00).getValueColor(context),
      ),
    );
  }

  Widget _buildSymbolText(BuildContext context, String symbol) {
    return Text(
      symbol,
      style: context.textTheme.regular.fs12,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceAndGainRow(BuildContext context, String price, String leadupGain) {
    final leadupGainValue = data?.leadupGain ?? 0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        FlipText(
          double.parse(price),
          fractionDigits: 3,
          style: context.textTheme.primary.fs12.w700.ffAkz.copyWith(
            color: (leadupGainValue).getValueColor(context),
          ),
        ),
        FlipText(
          double.parse(leadupGain),
          fractionDigits: 2,
          suffix: ' %',
          prefix: TradingUtils.getSign(leadupGainValue),
          style: context.textTheme.primary.fs12.w700.ffAkz.copyWith(
            color: (leadupGainValue).getValueColor(context),
          ),
        ),
      ],
    );
  }
}
