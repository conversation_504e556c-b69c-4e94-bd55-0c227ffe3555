import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import '../../../../../../config/flavors/app_config.dart';

class FractionSection extends StatelessWidget {
  /// A widget that displays a row of buttons for selecting the fraction of the available quantity to trade.
  const FractionSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(OrderFraction.values.length, (index) {
        return FractionButton(fraction: OrderFraction.values[index]);
      }),
    );
  }
}

class FractionButton extends StatelessWidget {
  const FractionButton({super.key, required this.fraction});
  final OrderFraction fraction;
  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, OrderFraction?>(
      selector: (state) => state.orderFraction,
      builder: (context, state) {
        final isSelected = state == fraction;
        return InkWell(
          // When tapped, set the order fraction to null if already selected,
          // otherwise set it to the current fraction value
          onTap: () {
            FocusScope.of(context).unfocus();
            context.read<TradingCubit>().setOrderFraction(
                  orderFraction: isSelected ? null : fraction,
                  showToast: true,
                );
          },
          child: Container(
            height: 30.gw,
            width: 75.gw,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.gr),
              border: Border.all(color: isSelected ? context.colorTheme.stockRed : context.theme.dividerColor),
            ),
            child: Center(
              child: Text(
                fraction.label,
                style: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kTemplateD => context.textTheme.title,
                  _ => context.textTheme.regular,
                }
                    .fs13,
              ),
            ),
          ),
        );
      },
    );
  }
}
