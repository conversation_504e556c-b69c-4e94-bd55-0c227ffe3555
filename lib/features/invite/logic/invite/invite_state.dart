import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/models/entities/invite/invite_info_entity.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/invite_detail_model.dart';

part 'invite_state.freezed.dart';

@freezed
class InviteState with _$InviteState {
  const factory InviteState({
    @Default(DataStatus.idle) DataStatus status,
    InviteDetailModel? inviteDetail,
    InviteInfoEntity? inviteInfo,
    String? error,
    String? customInviteLink,
  }) = _InviteState;
}
