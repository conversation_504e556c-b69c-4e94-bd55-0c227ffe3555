import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/shared/models/interest_coupon/interest_coupon_model.dart';

InterestCouponModel $InterestCouponModelFromJson(Map<String, dynamic> json) {
  final InterestCouponModel interestCouponModel = InterestCouponModel();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    interestCouponModel.id = id;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    interestCouponModel.amount = amount;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    interestCouponModel.currency = currency;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    interestCouponModel.status = status;
  }
  final String? validFrom = jsonConvert.convert<String>(json['validFrom']);
  if (validFrom != null) {
    interestCouponModel.validFrom = validFrom;
  }
  final String? validTo = jsonConvert.convert<String>(json['validTo']);
  if (validTo != null) {
    interestCouponModel.validTo = validTo;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    interestCouponModel.createTime = createTime;
  }
  final String? usageTime = jsonConvert.convert<String>(json['usageTime']);
  if (usageTime != null) {
    interestCouponModel.usageTime = usageTime;
  }
  final int? fromType = jsonConvert.convert<int>(json['fromType']);
  if (fromType != null) {
    interestCouponModel.fromType = fromType;
  }
  final String? notes = jsonConvert.convert<String>(json['notes']);
  if (notes != null) {
    interestCouponModel.notes = notes;
  }
  return interestCouponModel;
}

Map<String, dynamic> $InterestCouponModelToJson(InterestCouponModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['amount'] = entity.amount;
  data['currency'] = entity.currency;
  data['status'] = entity.status;
  data['validFrom'] = entity.validFrom;
  data['validTo'] = entity.validTo;
  data['createTime'] = entity.createTime;
  data['usageTime'] = entity.usageTime;
  data['fromType'] = entity.fromType;
  data['notes'] = entity.notes;
  return data;
}

extension InterestCouponModelExtension on InterestCouponModel {
  InterestCouponModel copyWith({
    int? id,
    double? amount,
    String? currency,
    int? status,
    String? validFrom,
    String? validTo,
    String? createTime,
    String? usageTime,
    int? fromType,
    String? notes,
  }) {
    return InterestCouponModel()
      ..id = id ?? this.id
      ..amount = amount ?? this.amount
      ..currency = currency ?? this.currency
      ..status = status ?? this.status
      ..validFrom = validFrom ?? this.validFrom
      ..validTo = validTo ?? this.validTo
      ..createTime = createTime ?? this.createTime
      ..usageTime = usageTime ?? this.usageTime
      ..fromType = fromType ?? this.fromType
      ..notes = notes ?? this.notes;
  }
}

InterestCouponListModel $InterestCouponListModelFromJson(
    Map<String, dynamic> json) {
  final InterestCouponListModel interestCouponListModel = InterestCouponListModel();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    interestCouponListModel.current = current;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    interestCouponListModel.hasNext = hasNext;
  }
  final List<InterestCouponModel>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<InterestCouponModel>(e) as InterestCouponModel)
      .toList();
  if (records != null) {
    interestCouponListModel.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    interestCouponListModel.total = total;
  }
  return interestCouponListModel;
}

Map<String, dynamic> $InterestCouponListModelToJson(
    InterestCouponListModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension InterestCouponListModelExtension on InterestCouponListModel {
  InterestCouponListModel copyWith({
    int? current,
    bool? hasNext,
    List<InterestCouponModel>? records,
    int? total,
  }) {
    return InterestCouponListModel()
      ..current = current ?? this.current
      ..hasNext = hasNext ?? this.hasNext
      ..records = records ?? this.records
      ..total = total ?? this.total;
  }
}