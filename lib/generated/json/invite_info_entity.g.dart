import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/invite/invite_info_entity.dart';

InviteInfoEntity $InviteInfoEntityFromJson(Map<String, dynamic> json) {
  final InviteInfoEntity inviteInfoEntity = InviteInfoEntity();
  final double? commissionRate = jsonConvert.convert<double>(json['commissionRate']);
  if (commissionRate != null) {
    inviteInfoEntity.commissionRate = commissionRate;
  }
  final double? interestRate = jsonConvert.convert<double>(json['interestRate']);
  if (interestRate != null) {
    inviteInfoEntity.interestRate = interestRate;
  }
  final String? inviteCode = jsonConvert.convert<String>(json['inviteCode']);
  if (inviteCode != null) {
    inviteInfoEntity.inviteCode = inviteCode;
  }
  final double? totalCommission = jsonConvert.convert<double>(json['totalCommission']);
  if (totalCommission != null) {
    inviteInfoEntity.totalCommission = totalCommission;
  }
  return inviteInfoEntity;
}

Map<String, dynamic> $InviteInfoEntityToJson(InviteInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['commissionRate'] = entity.commissionRate;
  data['interestRate'] = entity.interestRate;
  data['inviteCode'] = entity.inviteCode;
  data['totalCommission'] = entity.totalCommission;
  return data;
}

extension InviteInfoEntityExtension on InviteInfoEntity {
  InviteInfoEntity copyWith({
    double? commissionRate,
    double? interestRate,
    String? inviteCode,
    double? totalCommission,
  }) {
    return InviteInfoEntity()
      ..commissionRate = commissionRate ?? this.commissionRate
      ..interestRate = interestRate ?? this.interestRate
      ..inviteCode = inviteCode ?? this.inviteCode
      ..totalCommission = totalCommission ?? this.totalCommission;
  }
}