import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/routes/route_tracker.dart';
import 'package:gp_stock_app/shared/routes/routers.dart';

import 'core/theme/app_themes.dart';
import 'core/utils/engage_lab/engage_lab_app.dart';
import 'shared/logic/theme/theme_cubit.dart';
import 'shared/routes/app_router.dart';
import 'shared/widgets/flavor_banner.dart';

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EngageLabUtil engageLabUtil = EngageLabUtil();

  // Supported locales for easy switching in debug mode
  static const List<Locale> _debugLocales = [
    Locale('en', 'US'),
    Locale('zh', 'CN'),
    Locale('zh', 'HK'),
  ];

  int _currentLocaleIndex = 0;

  @override
  void initState() {
    if (AppConfig.instance.pushAppKey.isNotEmpty) {
      engageLabUtil.init(appKey: AppConfig.instance.pushAppKey, channel: "developer");
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Initialize GSScreenUtil
    context.initGSScreen(
      uiSize: const Size(375, 812), // Your design size
      maxWidth: 475, // Max width for tablet
    );

    return GestureDetector(
      onTap: () async => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return MaterialApp(
            // Navigation configuration
            navigatorObservers: [
              routeObserver,
              RouteTracker(),
            ],

            navigatorKey: getIt<NavigatorService>().navigatorKey,
            onGenerateRoute: (settings) {
              LogI("📌 Received route: ${settings.name}, args: ${settings.arguments}");

              return Routes.router.generator(settings);
            },
            onUnknownRoute: (settings) => Routes.router.generator(const RouteSettings(name: AppRouter.routeMain)),
            // onGenerateRoute: Routes.router.generator,
            initialRoute: AppRouter.splash,
            // Localization configuration
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            // Theme configuration
            theme: AppTheme.instance.getThemeLight(),
            darkTheme: AppTheme.instance.getThemeDark(),
            themeMode: state.themeMode,
            // App metadata
            title: AppConfig.instance.appName,
            debugShowCheckedModeBanner: false,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1),
                ),
                // 18.verticalSpace,
                child: FlutterEasyLoading(
                  child: FlavorBanner(
                    message: AppConfig.instance.flavorTitle,
                    show: kDebugMode,
                    child: Stack(
                      children: [
                        child!,
                        // if (kDebugMode) ..._buildDebugControls(context, state),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  /// Cycles through available locales in debug mode
  void _cycleLocale(BuildContext context) {
    if (!kDebugMode) return;

    _currentLocaleIndex = (_currentLocaleIndex + 1) % _debugLocales.length;
    final newLocale = _debugLocales[_currentLocaleIndex];
    context.setLocale(newLocale);
    getIt<UserCubit>().setCurrentLocale('${newLocale.languageCode}-${newLocale.countryCode}');
  }

  /// Toggles between light and dark theme
  void _toggleTheme(BuildContext context, ThemeState state) {
    final newTheme = state.themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    context.read<ThemeCubit>().changeTheme(newTheme);
  }

  /// Builds debug-only controls for theme and locale switching
  List<Widget> _buildDebugControls(BuildContext context, ThemeState state) {
    return [
      // Theme toggle button
      Positioned(
        top: 15,
        right: 10,
        child: FloatingActionButton(
          heroTag: 'theme_toggle',
          tooltip: 'Toggle Theme',
          onPressed: () => _toggleTheme(context, state),
          child: Icon(
            state.themeMode == ThemeMode.light ? Icons.dark_mode : Icons.light_mode,
          ),
        ),
      ),

      // Language toggle button
      Positioned(
        top: 15,
        left: 10,
        child: FloatingActionButton(
          heroTag: 'locale_toggle',
          tooltip: 'Change Language',
          onPressed: () => _cycleLocale(context),
          child: const Icon(Icons.language),
        ),
      ),
    ];
  }
}
