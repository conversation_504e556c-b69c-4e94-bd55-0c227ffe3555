---
type: "manual"
---

1. Theme System Usage
✅ ALWAYS use context.theme for basic theme colors (primaryColor, cardColor, scaffoldBackgroundColor, dividerColor)
✅ ALWAYS use context.colorTheme for custom app colors (textPrimary, textTitle, textRegular, stockRed, stockGreen, etc.)
✅ ALWAYS use context.textTheme for text styles with extensions (primary, title, regular, secondary, tertiary, etc.)
❌ NEVER hardcode colors like Color(0xFF525A79) - use theme colors instead
❌ NEVER hardcode font families like 'PingFang SC' - let theme handle fonts
2. Sizing & Spacing Rules
✅ ALWAYS use .gw for width-based sizing (horizontal spacing, widths)
✅ ALWAYS use .gw for height-based sizing also(vertical spacing, heights)
✅ ALWAYS use .gr for radius values
✅ ALWAYS use .gsp for font sizes (handled automatically by theme)
❌ NEVER use .gh for width - this was my mistake in the original implementation
❌ Don't Use extensions like 16.verticalSpace, 12.horizontalSpace for spacing use SizedBox() instead
3. Dialog Structure Patterns
4. Text Styling Rules
❌ No need to use again the default styling like .fs14 and .w400 
    // ✅ CORRECT - Use theme with extensions
    Text(
    'title'.tr(),
    style: context.textTheme.primary.fs16.w600,
    )

    // ❌ WRONG - Hardcoded styles
    Text(
    'title',
    style: TextStyle(
        fontFamily: 'PingFang SC',
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Color(0xFF525A79),
    ),
    )
5. Color Usage Rules
    // ✅ CORRECT - Theme colors
    backgroundColor: context.theme.cardColor,
    textColor: context.colorTheme.textPrimary,
    primaryColor: context.theme.primaryColor,
    stockGreen: context.colorTheme.stockGreen,

    // ❌ WRONG - Hardcoded colors (only for exceptional case)
    backgroundColor: Color(0xFFFFFFFF),
    textColor: Color(0xFF525A79),
6. Button Implementation Rules
✅ Use existing button widgets: CommonButton, CustomMaterialButton
✅ Follow button style enums: CommonButtonStyle.primary, CommonButtonStyle.outlined
✅ Use theme colors for button backgrounds and text
❌ Don't create custom button widgets without checking existing ones first
7. Modal & Bottom Sheet Rules
    // ✅ CORRECT Pattern for Modal Bottom Sheets
    showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent, // Let child handle color
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
    ),
    builder: (context) => Container(
        decoration: BoxDecoration(
        color: context.theme.cardColor, // Use theme
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.gr)),
        ),
        // Content
    ),
    );
8. Translation Rules
✅ ALWAYS use .tr() for user-facing text
✅ Add translations to all language files (zh-CN.json, en-US.json, zh-HK.json)
✅ Use descriptive keys like interestCouponUnused not unused
❌ Never hardcode text in Chinese or English
9. Import Rules
    // ✅ REQUIRED imports for dialogs
    import 'package:easy_localization/easy_localization.dart';
    import 'package:flutter/material.dart';
    import 'package:gp_stock_app/core/theme/app_themes.dart'; // For theme access
    import 'package:gp_stock_app/core/utils/screen_util.dart'; // For sizing
10. Container & Layout Rules
✅ Use ShadowBox instead of manual Container with shadows
✅ Use Material(type: MaterialType.transparency) for dialog wrappers
✅ Use mainAxisSize: MainAxisSize.min for dialog columns
✅ Use proper padding/margin with .gw extensions
11. State Management in widgets
✅ Use StatelessWidget when possible for simple widgets
✅ Use StatefulWidget only when internal state is needed
✅ Pass callbacks for actions instead of handling navigation inside widgets
12. Animation & Interaction Rules
✅ Use HapticFeedback.lightImpact() for button taps
✅ Use Navigator.pop(context) to close dialogs
✅ Use InkWell or GestureDetector for custom tap areas
✅ Add debouncing for buttons that trigger network calls
13. Error Handling in Dialogs
✅ Show loading states with existing loading widgets
✅ Handle empty states with proper messaging
✅ Use consistent error styling with theme colors
14. File Organization Rules
✅ Place widgets in lib/shared/widgets/ for reusable ones
✅ Place feature-specific widgets in feature folders
✅ Create helper functions like showMyDialog() for easy usage
15. Testing & Demo Rules
✅ Test with different themes (light/dark)
✅ Test with different screen sizes
✅ Verify translations work correctly
16. Network 